name: arabic_sign_language
description: "A new Flutter project."
publish_to: "none"
version: 1.0.14+1

environment:
  sdk: ">=3.2.6 <4.0.0"

dependencies:
  asl_flutter_input:
    path: /Users/<USER>/Documents/gitlab
      asl/ASL-14082024/asl-audio-streaming-to-text/asl_flutter_input
  audio_video_progress_bar: ^2.0.3
  audio_waveforms: ^1.0.5
  audioplayers: ^6.4.0
  bloc: ^8.1.4
  blur: ^4.0.0
  carousel_slider: ^4.2.1
  chewie: ^1.8.7
  collection: ^1.18.0
  country_code_picker: ^3.0.0
  country_picker: ^2.0.27
  cupertino_icons: ^1.0.2
  device_info_plus: ^11.3.0
  dio: ^5.5.0+1
  easy_localization: ^3.0.7+1
  equatable: ^2.0.5
  file_picker: ^8.3.2
  firebase_analytics: ^11.3.3
  firebase_auth: ^5.6.0
  firebase_core: ^3.6.0
  firebase_database: ^11.1.6
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_launcher_icons: ^0.14.4
  flutter_secure_storage: ^4.2.1
  flutter_unity_widget:
    git:
      url: https://github.com/juicycleff/flutter-unity-view-widget.git
      ref: flutter_3.24_android_hotfix
  go_router: ^15.1.2
  google_fonts: ^6.2.1
  google_sign_in: ^6.2.2
  image_picker: ^1.1.2
  internet_connection_checker: ^1.0.0+1
  introduction_screen: ^3.1.17
  json_annotation: ^4.9.0
  just_audio: ^0.9.46
  logger: ^2.6.0
  meta: ^1.12.0
  onboarding: ^4.0.2
  package_info_plus: ^8.1.1
  path: ^1.9.0
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  scrollable_positioned_list: ^0.3.8
  shared_preferences: ^2.2.3
  showcaseview: ^3.0.0
  speech_to_text: ^7.0.0
  url_launcher: ^6.3.1
  video_player: ^2.9.5
  wakelock_plus: ^1.2.8
  youtube_explode_dart: ^2.3.0
  youtube_player_flutter: ^8.1.2

# dependency_overrides:
#   webview_flutter_android: 3.16.1
dev_dependencies:
  build_runner: ^2.4.11
  change_app_package_name: ^1.3.0
  flutter_lints: ^2.0.0
  flutter_test:
    sdk: flutter
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/enhancement/
    - assets/icons/
    - assets/lang/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: fonts/poppins/Poppins-Black.ttf
        - asset: fonts/poppins/Poppins-BlackItalic.ttf
        - asset: fonts/poppins/Poppins-Bold.ttf
        - asset: fonts/poppins/Poppins-BoldItalic.ttf
        - asset: fonts/poppins/Poppins-ExtraBold.ttf
        - asset: fonts/poppins/Poppins-ExtraBoldItalic.ttf
        - asset: fonts/poppins/Poppins-ExtraLight.ttf
        - asset: fonts/poppins/Poppins-ExtraLightItalic.ttf
        - asset: fonts/poppins/Poppins-Italic.ttf
        - asset: fonts/poppins/Poppins-Light.ttf
        - asset: fonts/poppins/Poppins-LightItalic.ttf
        - asset: fonts/poppins/Poppins-Medium.ttf
        - asset: fonts/poppins/Poppins-MediumItalic.ttf
        - asset: fonts/poppins/Poppins-Regular.ttf
        - asset: fonts/poppins/Poppins-SemiBold.ttf
        - asset: fonts/poppins/Poppins-SemiBoldItalic.ttf
        - asset: fonts/poppins/Poppins-Thin.ttf
        - asset: fonts/poppins/Poppins-ThinItalic.ttf

    - family: Inter
      fonts:
        - asset: fonts/inter/Inter-Black.ttf
        - asset: fonts/inter/Inter-Bold.ttf
        - asset: fonts/inter/Inter-ExtraBold.ttf
        - asset: fonts/inter/Inter-ExtraLight.ttf
        - asset: fonts/inter/Inter-Light.ttf
        - asset: fonts/inter/Inter-Medium.ttf
        - asset: fonts/inter/Inter-Regular.ttf
        - asset: fonts/inter/Inter-SemiBold.ttf
        - asset: fonts/inter/Inter-Thin.ttf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
