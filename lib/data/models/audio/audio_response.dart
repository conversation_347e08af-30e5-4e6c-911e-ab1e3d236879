import 'package:arabic_sign_language/data/models/video_transcription/transcription_item.dart';
import 'package:json_annotation/json_annotation.dart';

part 'audio_response.g.dart';

@JsonSerializable()
class AudioResponse {
  final List<AudioSegment> data;
  final bool status;
  final String? message;
  final String? detail;

  AudioResponse(
      {required this.data, required this.status, this.message, this.detail});

  factory AudioResponse.fromJson(Map<String, dynamic> json) =>
      _$AudioResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AudioResponseToJson(this);
}

@JsonSerializable()
class AudioSegment implements TranscriptionItem {
  final int id;
  @Json<PERSON>ey(name: 'audio_id')
  final int audioId;
  @Json<PERSON>ey(name: 'start_time')
  final double startTime;
  @Json<PERSON>ey(name: 'end_time')
  final double endTime;
  final List<String> word;
  final List<String> root;
  final String status;
  @Json<PERSON>ey(name: 'arabic_word_flags')
  final List<bool> arabicWordFlags;

  AudioSegment({
    required this.id,
    required this.audioId,
    required this.startTime,
    required this.endTime,
    required this.word,
    required this.root,
    required this.status,
    required this.arabicWordFlags,
  });

  factory AudioSegment.fromJson(Map<String, dynamic> json) =>
      _$AudioSegmentFromJson(json);

  Map<String, dynamic> toJson() => _$AudioSegmentToJson(this);
}
