// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AudioResponse _$AudioResponseFromJson(Map<String, dynamic> json) =>
    AudioResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => AudioSegment.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as bool,
      message: json['message'] as String?,
      detail: json['detail'] as String?,
    );

Map<String, dynamic> _$AudioResponseToJson(AudioResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'message': instance.message,
      'detail': instance.detail,
    };

AudioSegment _$AudioSegmentFromJson(Map<String, dynamic> json) => AudioSegment(
      id: (json['id'] as num).toInt(),
      audioId: (json['audio_id'] as num).toInt(),
      startTime: (json['start_time'] as num).toDouble(),
      endTime: (json['end_time'] as num).toDouble(),
      word: (json['word'] as List<dynamic>).map((e) => e as String).toList(),
      root: (json['root'] as List<dynamic>).map((e) => e as String).toList(),
      status: json['status'] as String,
      arabicWordFlags: (json['arabic_word_flags'] as List<dynamic>)
          .map((e) => e as bool)
          .toList(),
    );

Map<String, dynamic> _$AudioSegmentToJson(AudioSegment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'audio_id': instance.audioId,
      'start_time': instance.startTime,
      'end_time': instance.endTime,
      'word': instance.word,
      'root': instance.root,
      'status': instance.status,
      'arabic_word_flags': instance.arabicWordFlags,
    };
