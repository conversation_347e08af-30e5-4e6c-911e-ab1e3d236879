import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

part 'connection_checker_event.dart';
part 'connection_checker_state.dart';

class ConnectionCheckerBloc
    extends Bloc<ConnectionCheckerEvent, ConnectionCheckerState> {
  ConnectionCheckerBloc() : super(ConnectionCheckerInitial()) {
    on<ConnectivityChanged>(onConnectivityChanged);
    on<ConnectivityObserver>(onStartConnectionChecking);
  }

  void onStartConnectionChecking(
      ConnectivityObserver event, Emitter<ConnectionCheckerState> emit) {
    final connectionChecker = InternetConnectionChecker.createInstance();
    InternetConnectionStatus? previousStatus;
    connectionChecker.onStatusChange.listen((InternetConnectionStatus status) {
      print("Status => $status");
      if (previousStatus != null && status != previousStatus) {
        if (status == InternetConnectionStatus.connected) {
          add(ConnectivityChanged(status: true));
        } else {
          add(ConnectivityChanged(status: false));
        }
      }
      previousStatus = status;
    });
  }

  void onConnectivityChanged(
      ConnectivityChanged event, Emitter<ConnectionCheckerState> emit) {
    emit(ConnectivityStatusChanged(
      isNetworkConnected: event.status,
    ));
  }
}
