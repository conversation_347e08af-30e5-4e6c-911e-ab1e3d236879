part of 'video_transcription_bloc.dart';

abstract class VideoTranscriptionState extends Equatable {
  final String currentAnimation;
  final bool isTextFieldEnabled;
  final bool isTranslateButtonDisabled;
  final bool isDataLoading;
  final double animationSpeed;
  final bool isDisableInput;
  final bool isVideoSelected;
  final bool isVideoUploading;
  final bool showControls;
  // final int uniqueId;

  const VideoTranscriptionState({
    this.currentAnimation = '',
    this.isTextFieldEnabled = true,
    this.isTranslateButtonDisabled = false,
    this.isDataLoading = false,
    this.animationSpeed = 1.0,
    this.isDisableInput = false,
    this.isVideoSelected = false,
    this.isVideoUploading = false,
    this.showControls = false,
    // this.uniqueId = 0,
  });

  @override
  List<Object?> get props => [
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        showControls,
        // uniqueId,
      ];
}

final class VideoTranscriptionInitial extends VideoTranscriptionState {}

final class VideoTranscriptionStarted extends VideoTranscriptionState {
  const VideoTranscriptionStarted({
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isDisableInput,
    super.isVideoSelected = false,
    super.isVideoUploading = false,
    super.showControls = false,
  });
}

/// Success state when video transcription is completed successfully
final class VideoTranscriptionSuccess extends VideoTranscriptionState {
  final List<Data> captionList;
  final int sessionId;
  final String videoId;

  const VideoTranscriptionSuccess({
    required this.captionList,
    required this.sessionId,
    required this.videoId,
    super.currentAnimation,
    super.isTextFieldEnabled = true,
    super.isTranslateButtonDisabled = false,
    super.isDataLoading = false,
    super.animationSpeed,
    super.isDisableInput = false,
    super.isVideoSelected = false,
    super.isVideoUploading = false,
    super.showControls = false,
  });

  @override
  List<Object?> get props => [
        captionList,
        sessionId,
        videoId,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}

/// Error state when video transcription fails
final class VideoTranscriptionScreenError extends VideoTranscriptionState {
  final String message;

  const VideoTranscriptionScreenError({
    required this.message,
    super.currentAnimation,
    super.isTextFieldEnabled = true,
    super.isTranslateButtonDisabled = false,
    super.isDataLoading = false,
    super.animationSpeed,
    super.isDisableInput = false,
    super.isVideoSelected = false,
    super.isVideoUploading = false,
    super.showControls = false,
  });

  @override
  List<Object?> get props => [
        message,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}

/// State for sending messages to video screen
final class SendMessagesToVideoScreen extends VideoTranscriptionState {
  final Map<String, dynamic> message;

  const SendMessagesToVideoScreen({
    required this.message,
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isDisableInput,
    super.isVideoSelected,
    super.isVideoUploading,
    super.showControls,
    // super.uniqueId,
  });

  @override
  List<Object?> get props => [
        message,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
        // uniqueId
      ];
}

/// State for updating current animation
final class UpdateCurrentAnimation extends VideoTranscriptionState {
  const UpdateCurrentAnimation({
    required super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isDisableInput,
    super.isVideoSelected,
    super.isVideoUploading,
    super.showControls,
  });

  @override
  List<Object?> get props => [
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}

class AnimationSpeedUpdated extends VideoTranscriptionState {
  const AnimationSpeedUpdated({
    required double animationSpeed,
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.isDisableInput,
    super.isVideoSelected,
    super.isVideoUploading,
    super.showControls,
  }) : super(animationSpeed: animationSpeed);

  @override
  List<Object?> get props => [
        animationSpeed,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}

class SliderValueUpdated extends VideoTranscriptionState {
  final double sliderValue;

  const SliderValueUpdated({
    required this.sliderValue,
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isDisableInput,
    super.isVideoSelected,
    super.isVideoUploading,
    super.showControls,
  });

  @override
  List<Object?> get props => [
        sliderValue,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}

class VideoSelected extends VideoTranscriptionState {
  const VideoSelected(
      {super.currentAnimation,
      super.isTextFieldEnabled,
      super.isTranslateButtonDisabled,
      super.isDataLoading,
      super.animationSpeed,
      super.isDisableInput,
      super.isVideoSelected,
      super.isVideoUploading,
      super.showControls});

  @override
  List<Object?> get props => [
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}

class FileUploadLoadingState extends VideoTranscriptionState {
  const FileUploadLoadingState({
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.animationSpeed,
    super.isDisableInput,
    super.isVideoSelected,
    super.isDataLoading,
    super.showControls,
  }) : super(isVideoUploading: true);

  @override
  List<Object?> get props => [
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}

class VideoTranscriptionLoaded extends VideoTranscriptionState {
  const VideoTranscriptionLoaded({
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isDisableInput,
    super.isVideoSelected,
    super.isVideoUploading,
    super.showControls,
  });

  @override
  List<Object?> get props => [
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isDisableInput,
        isVideoSelected,
        isVideoUploading,
        showControls,
      ];
}
