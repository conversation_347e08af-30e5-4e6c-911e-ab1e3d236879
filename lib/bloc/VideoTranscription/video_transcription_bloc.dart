import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../../data/models/audio/audio_response.dart';
import '../../data/models/audio/upload_session_response.dart';
import '../../data/service/recoding_service.dart';
import '../../data/service/transcript_service.dart';
import '../../data/models/video_transcription/transcription_item.dart';

part 'video_transcription_event.dart';
part 'video_transcription_state.dart';

class VideoTranscriptionBloc
    extends Bloc<VideoTranscriptionEvent, VideoTranscriptionState> {
  final TranscriptService transcriptService;
  final RecordingService recordingService;
  List<Data> captionList = [];
  Timer? _timer;
  List<String> rootWords = [];
  List<bool> arabicTextFlags = [];
  bool isManuallyPaused = false;
  bool currentAnimationCompleted = false;
  bool isYouTubePlayerActive = false;
  double animationSpeed = 1.0;
  Timer? audioChunkTimer;
  List<AudioSegment> audioResponseList = [];
  bool isChunkUploading = false;
  bool isChunkDataProcessing = false;

  VideoTranscriptionBloc({
    required this.transcriptService,
    required this.recordingService,
  }) : super(VideoTranscriptionInitial()) {
    on<VideoTranscriptionEvent>((event, emit) {});
    on<StartVideoTranscription>(_onStartVideoTranscription);
    on<ProcessVideoTranscription>(_onProcessVideoTranscription);
    on<CheckTranscriptionStatus>(_onCheckTranscriptionStatus);
    on<FetchTranscript>(_onFetchTranscript);
    on<UpdateCurrentAnimationText>(_onUpdateCurrentAnimationText);
    on<VideoTranscriptionCompleted>(_onVideoTranscriptionCompleted);
    on<VideoTranscriptionError>(_onVideoTranscriptionError);
    on<ResetVideoTranscription>(_onResetVideoTranscription);
    on<ToggleInputVisibility>(_onToggleInputVisibility);
    on<AddVideoListener>(_onAddVideoListener);
    on<SendVideoMessage>(_onSendVideoMessage);
    on<YouTubePlayerReady>(_onYouTubePlayerReady);
    on<UpdateAnimationSpeed>(_onUpdateAnimationSpeed);
    on<UpdateSliderValue>(_onUpdateSliderValue);
    on<UpdateVideoSelection>(_onUpdateVideoSelection);
    on<UploadChunkData>(_onUploadChunkData);
    on<AddChewieControllerListener>(_onAddChewieControllerListener);
  }

  FutureOr<void> _onStartVideoTranscription(StartVideoTranscription event,
      Emitter<VideoTranscriptionState> emit) async {
    emit(
      const VideoTranscriptionStarted(
          isTextFieldEnabled: false,
          isTranslateButtonDisabled: true,
          isDataLoading: true),
    );

    await FirebaseAnalytics.instance
        .logEvent(name: "video_transcription_started", parameters: {
      "videoId": event.videoId,
      "status": "started",
      "timeStamp": DateTime.now().toString()
    });

    captionList = [];
    final audioStatus = await transcriptService.getVideoStatus(event.videoId);

    if (audioStatus.status) {
      if (audioStatus.data.isNotEmpty) {
        event.controller.clear();
        captionList.addAll(audioStatus.data);
        emit(VideoTranscriptionSuccess(
          captionList: audioStatus.data,
          sessionId: audioStatus.sessionId,
          videoId: event.videoId,
          currentAnimation: state.currentAnimation,
          isTextFieldEnabled: state.isTextFieldEnabled,
          isTranslateButtonDisabled: state.isTranslateButtonDisabled,
          isDataLoading: state.isDataLoading,
          isDisableInput: true,
          animationSpeed: state.animationSpeed,
          isVideoSelected: state.isVideoSelected,
          isVideoUploading: state.isVideoUploading,
        ));

        add(CheckTranscriptionStatus(sessionId: audioStatus.sessionId));

        await FirebaseAnalytics.instance
            .logEvent(name: "video_transcription_fetched", parameters: {
          "videoId": event.videoId,
          "status": "returned cached data",
          "timeStamp": DateTime.now().toString()
        });
      } else {
        final data = await transcriptService.getTranscript(
          audioStatus.sessionId,
        );
        event.controller.clear();

        captionList.addAll(data.data);

        emit(VideoTranscriptionSuccess(
          captionList: data.data,
          sessionId: audioStatus.sessionId,
          videoId: event.videoId,
          currentAnimation: state.currentAnimation,
          isDataLoading: state.isDataLoading,
          isDisableInput: true,
          animationSpeed: state.animationSpeed,
          isVideoSelected: state.isVideoSelected,
          isVideoUploading: state.isVideoUploading,
        ));

        add(CheckTranscriptionStatus(sessionId: audioStatus.sessionId));

        await FirebaseAnalytics.instance
            .logEvent(name: "video_transcription_fetch", parameters: {
          "videoId": event.videoId,
          "status": "Completed",
          "timeStamp": DateTime.now().toString()
        });
      }
    } else {
      final value = await transcriptService.uploadAudioFileToTranscript(
          event.videoId, audioStatus.trackingId);

      if (value.status) {
        await FirebaseAnalytics.instance
            .logEvent(name: "video_uploading_started", parameters: {
          "videoId": event.videoId,
          "status": "fetching captions from audio",
          "timeStamp": DateTime.now().toString()
        });

        await Future.delayed(const Duration(seconds: 30));

        final data = await transcriptService.getTranscript(
          value.sessionId,
        );

        if (data.data.isNotEmpty) {
          event.controller.clear();
          captionList.addAll(data.data);
          emit(VideoTranscriptionSuccess(
            captionList: data.data,
            sessionId: value.sessionId,
            videoId: event.videoId,
            currentAnimation: state.currentAnimation,
            isDataLoading: state.isDataLoading,
            isDisableInput: true,
            animationSpeed: state.animationSpeed,
            isVideoSelected: state.isVideoSelected,
            isVideoUploading: state.isVideoUploading,
          ));
          add(CheckTranscriptionStatus(sessionId: value.sessionId));

          await FirebaseAnalytics.instance
              .logEvent(name: "video_transcription_completed", parameters: {
            "videoId": event.videoId,
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });
        }
      } else {
        emit(VideoTranscriptionScreenError(
          message: "Error Occurred, Please try again",
          isDataLoading: false,
          animationSpeed: state.animationSpeed,
        ));
      }
    }
  }

  FutureOr<void> _onProcessVideoTranscription(ProcessVideoTranscription event,
      Emitter<VideoTranscriptionState> emit) async {
    // Process the transcription data
    for (var caption in event.captionList) {
      if (caption.root != null && caption.root!.isNotEmpty) {
        for (var rootWord in caption.root!) {
          emit(SendMessagesToVideoScreen(
            message: {'root': rootWord, 'word': caption.word},
            currentAnimation: state.currentAnimation,
            isTextFieldEnabled: state.isTextFieldEnabled,
            isTranslateButtonDisabled: state.isTranslateButtonDisabled,
            isDataLoading: state.isDataLoading,
            animationSpeed: state.animationSpeed,
            isDisableInput: state.isDisableInput,
            isVideoSelected: state.isVideoSelected,
            isVideoUploading: state.isVideoUploading,
          ));

          // Add a small delay between animations
          await Future.delayed(const Duration(milliseconds: 150));
        }
      }
    }
  }

  FutureOr<void> _onCheckTranscriptionStatus(
      CheckTranscriptionStatus event, Emitter<VideoTranscriptionState> emit) {
    _timer = Timer.periodic(const Duration(seconds: 3), (_) async {
      try {
        final lastCaption = captionList.isNotEmpty ? captionList.last : null;

        if (lastCaption != null && lastCaption.status == 'IN PROGRESS') {
          add(FetchTranscript(
            sessionId: event.sessionId,
            startTime: lastCaption.endTime.toInt(),
          ));
        } else {
          _timer?.cancel();
        }
      } catch (e) {
        print('Error checking transcript status: $e');
        _timer?.cancel();
      }
    });
  }

  FutureOr<void> _onFetchTranscript(
      FetchTranscript event, Emitter<VideoTranscriptionState> emit) async {
    try {
      final captions = await transcriptService.getTranscript(
        event.sessionId,
        startTime: event.startTime,
      );

      if (captions.data.isNotEmpty) {
        final updatedCaptionList = List<Data>.from(captionList)
          ..addAll(captions.data);
        captionList = updatedCaptionList;

        emit(VideoTranscriptionSuccess(
          captionList: updatedCaptionList,
          sessionId: event.sessionId,
          videoId: '',
          currentAnimation: state.currentAnimation,
          isDataLoading: state.isDataLoading,
          isDisableInput: state.isDisableInput,
          animationSpeed: state.animationSpeed,
          isTextFieldEnabled: state.isTextFieldEnabled,
          isTranslateButtonDisabled: state.isTranslateButtonDisabled,
          isVideoSelected: state.isVideoSelected,
          isVideoUploading: state.isVideoUploading,
        ));
      }
    } catch (e) {
      print('Error fetching transcript: $e');
    }
  }

  FutureOr<void> _onUpdateCurrentAnimationText(
      UpdateCurrentAnimationText event, Emitter<VideoTranscriptionState> emit) {
    emit(UpdateCurrentAnimation(
      currentAnimation: event.animationText,
      isTextFieldEnabled: state.isTextFieldEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isDataLoading: state.isDataLoading,
      animationSpeed: state.animationSpeed,
      isDisableInput: state.isDisableInput,
      isVideoSelected: state.isVideoSelected,
      isVideoUploading: state.isVideoUploading,
    ));
  }

  FutureOr<void> _onVideoTranscriptionCompleted(
      VideoTranscriptionCompleted event,
      Emitter<VideoTranscriptionState> emit) {
    emit(VideoTranscriptionSuccess(
      captionList: event.captionList,
      sessionId: event.sessionId,
      videoId: event.videoId,
      currentAnimation: state.currentAnimation,
      isDataLoading: false,
      isDisableInput: false,
      isVideoSelected: state.isVideoSelected,
      isVideoUploading: state.isVideoUploading,
    ));
  }

  FutureOr<void> _onVideoTranscriptionError(
      VideoTranscriptionError event, Emitter<VideoTranscriptionState> emit) {
    emit(VideoTranscriptionScreenError(
      message: event.message,
      isDataLoading: false,
      animationSpeed: state.animationSpeed,
    ));
  }

  FutureOr<void> _onResetVideoTranscription(
      ResetVideoTranscription event, Emitter<VideoTranscriptionState> emit) {
    // Cancel any running timer
    _timer?.cancel();

    // Clear caption list and other properties
    captionList.clear();
    rootWords.clear();
    isManuallyPaused = false;
    currentAnimationCompleted = false;
    isYouTubePlayerActive = false;

    // Clear text controller if provided
    event.controller?.clear();

    // Emit initial state to reset everything to default
    emit(VideoTranscriptionInitial());
  }

  FutureOr<void> _onToggleInputVisibility(
      ToggleInputVisibility event, Emitter<VideoTranscriptionState> emit) {
    // Reset YouTube player active flag when toggling to show inputs
    if (event.showInput) {
      isYouTubePlayerActive = false;
    }

    // Toggle the input visibility by updating isDisableInput
    if (state is VideoTranscriptionSuccess) {
      final currentState = state as VideoTranscriptionSuccess;
      emit(VideoTranscriptionSuccess(
        captionList: currentState.captionList,
        sessionId: currentState.sessionId,
        videoId: currentState.videoId,
        currentAnimation: currentState.currentAnimation,
        isTextFieldEnabled: currentState.isTextFieldEnabled,
        isTranslateButtonDisabled: currentState.isTranslateButtonDisabled,
        isDataLoading: currentState.isDataLoading,
        animationSpeed: currentState.animationSpeed,
        isDisableInput: !event
            .showInput, // Invert because showInput=true means isDisableInput=false
        isVideoSelected: currentState.isVideoSelected,
        isVideoUploading: currentState.isVideoUploading,
      ));
    } else if (state is VideoTranscriptionStarted) {
      final currentState = state as VideoTranscriptionStarted;
      emit(VideoTranscriptionStarted(
        currentAnimation: currentState.currentAnimation,
        isTextFieldEnabled: currentState.isTextFieldEnabled,
        isTranslateButtonDisabled: currentState.isTranslateButtonDisabled,
        isDataLoading: currentState.isDataLoading,
        animationSpeed: currentState.animationSpeed,
        isDisableInput: !event
            .showInput, // Invert because showInput=true means isDisableInput=false
        isVideoSelected: currentState.isVideoSelected,
        isVideoUploading: currentState.isVideoUploading,
      ));
    } else {
      // For other states, emit a new started state with the toggled visibility
      emit(VideoTranscriptionStarted(
        isDisableInput: !event.showInput,
        animationSpeed: state.animationSpeed,
      ));
    }
  }

  void _onAddVideoListener(
      AddVideoListener event, Emitter<VideoTranscriptionState> emit) {
    final youtubePlayerController = event.controller;
    List<Map<String, dynamic>> words = [];
    int? lastPausedTime;
    youtubePlayerController.addListener(() {
      if (youtubePlayerController.value.isPlaying) {
        final currentTime = youtubePlayerController.value.position.inSeconds;
        final currentTextData = captionList.firstWhere((element) {
          return element.startTime <= currentTime &&
              element.endTime >= currentTime;
        },
            orElse: () => Data(
                  id: 0,
                  videoId: 0,
                  startTime: 0,
                  endTime: 0,
                  word: [],
                  root: [],
                  status: "",
                  arabicWordFlags: [],
                ));
        // Check if the word is already in the list before adding
        if (currentTextData.endTime.ceil() - 1 == currentTime &&
            currentTextData.id != 0) {
          if (lastPausedTime !=
              youtubePlayerController.value.position.inSeconds) {
            lastPausedTime = youtubePlayerController.value.position.inSeconds;
            if (!currentAnimationCompleted) {
              youtubePlayerController.pause();
            } else {
              currentAnimationCompleted = false;
            }
          }
        }
        if (currentTextData.word.isNotEmpty &&
            (words.isEmpty ||
                words.last['start_time'] != currentTextData.startTime)) {
          print("currentTextData => ${currentTextData.word}");
          words.add({
            "word": currentTextData.word,
            "start_time": currentTextData.startTime
          });
          for (var value in currentTextData.arabicWordFlags) {
            arabicTextFlags.add(value);
          }
          for (var item in currentTextData.root) {
            rootWords.add(item);
            if (item.contains(',')) {
              final splitWords = item.split(',');
              for (var word in splitWords) {
                add(SendVideoMessage(message: {
                  'root': word.toString().trim(),
                  'word': currentTextData.word.toString()
                }));
              }
            } else {
              add(SendVideoMessage(message: {
                'root': item.toString().trim(),
                'word': currentTextData.word.toString()
              }));
            }
          }
        }
      }
    });
  }

  FutureOr<void> _onAddChewieControllerListener(
      AddChewieControllerListener event,
      Emitter<VideoTranscriptionState> emit) {
    VideoPlayerController chewieController = event.controller;
    List<Map<String, dynamic>> words = [];
    int? lastPausedTime;
    chewieController.addListener(() {
      if (chewieController.value.isPlaying) {
        final currentTime = chewieController.value.position.inSeconds;
        final currentTextData = audioResponseList.firstWhere((element) {
          return element.startTime <= currentTime &&
              element.endTime >= currentTime;
        },
            orElse: () => AudioSegment(
                  id: 0,
                  audioId: 0,
                  startTime: 0.0,
                  endTime: 0.0,
                  word: [],
                  root: [],
                  status: "",
                  arabicWordFlags: [],
                ));
        // Check if the word is already in the list before adding
        if (currentTextData.endTime.ceil() - 1 == currentTime &&
            currentTextData.id != 0) {
          if (lastPausedTime != chewieController.value.position.inSeconds) {
            lastPausedTime = chewieController.value.position.inSeconds;
            if (!currentAnimationCompleted) {
              chewieController.pause();
            } else {
              currentAnimationCompleted = false;
            }
          }
        }
        if (currentTextData.word.isNotEmpty &&
            (words.isEmpty ||
                words.last['start_time'] != currentTextData.startTime)) {
          words.add({
            "word": currentTextData.word,
            "start_time": currentTextData.startTime
          });
          for (var value in currentTextData.arabicWordFlags) {
            arabicTextFlags.add(value);
          }
          for (var item in currentTextData.root) {
            rootWords.add(item);
            print("_onAddChewieControllerListener 1 => $item");
            if (item.contains(',')) {
              final splitWords = item.split(',');
              print("_onAddChewieControllerListener 2 => $splitWords");
              for (var word in splitWords) {
                print("_onAddChewieControllerListener 3 => $word");
                add(SendVideoMessage(message: {
                  'root': word.toString().trim(),
                  'word': currentTextData.word.toString()
                }));
              }
            } else {
              print("_onAddChewieControllerListener 2 => $item");
              add(SendVideoMessage(message: {
                'root': item.toString().trim(),
                'word': currentTextData.word.toString()
              }));
            }
          }
        }
      }
    });
  }

  FutureOr<void> _onSendVideoMessage(
      SendVideoMessage event, Emitter<VideoTranscriptionState> emit) {
    // int uniqueId = 0;
    // Emit the message to be sent to Unity
    emit(SendMessagesToVideoScreen(
      message: event.message,
      currentAnimation: state.currentAnimation,
      isTextFieldEnabled: state.isTextFieldEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isDataLoading: state.isDataLoading,
      animationSpeed: state.animationSpeed,
      isDisableInput: state.isDisableInput,
      isVideoSelected: state.isVideoSelected,
      isVideoUploading: state.isVideoUploading,
      // uniqueId: uniqueId++,
    ));
  }

  FutureOr<void> _onYouTubePlayerReady(
      YouTubePlayerReady event, Emitter<VideoTranscriptionState> emit) {
    // Add the video listener
    add(AddVideoListener(controller: event.controller));

    // Mark YouTube player as active
    isYouTubePlayerActive = true;

    // Hide input widgets when YouTube player is ready
    if (state is VideoTranscriptionSuccess) {
      final currentState = state as VideoTranscriptionSuccess;
      emit(VideoTranscriptionSuccess(
        captionList: currentState.captionList,
        sessionId: currentState.sessionId,
        videoId: currentState.videoId,
        currentAnimation: currentState.currentAnimation,
        isTextFieldEnabled: currentState.isTextFieldEnabled,
        isTranslateButtonDisabled: currentState.isTranslateButtonDisabled,
        isDataLoading: currentState.isDataLoading,
        animationSpeed: currentState.animationSpeed,
        isDisableInput: true, // Hide input widgets when YouTube player is ready
        isVideoSelected: currentState.isVideoSelected,
        isVideoUploading: currentState.isVideoUploading,
      ));
    } else if (state is VideoTranscriptionStarted) {
      // If we're still in started state, just hide the inputs
      emit(VideoTranscriptionStarted(
        currentAnimation: state.currentAnimation,
        isTextFieldEnabled: state.isTextFieldEnabled,
        isTranslateButtonDisabled: state.isTranslateButtonDisabled,
        isDataLoading: state.isDataLoading,
        animationSpeed: state.animationSpeed,
        isDisableInput: true, // Hide input widgets when YouTube player is ready
        isVideoSelected: state.isVideoSelected,
        isVideoUploading: state.isVideoUploading,
      ));
    }
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }

  FutureOr<void> _onUpdateAnimationSpeed(
      UpdateAnimationSpeed event, Emitter<VideoTranscriptionState> emit) {
    animationSpeed = event.value.clamp(0.5, 2.0);

    emit(AnimationSpeedUpdated(
      animationSpeed: animationSpeed,
      currentAnimation: state.currentAnimation,
      isTextFieldEnabled: state.isTextFieldEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isDataLoading: state.isDataLoading,
      isDisableInput: state.isDisableInput,
      isVideoSelected: state.isVideoSelected,
      isVideoUploading: state.isVideoUploading,
    ));
  }

  // Add new event handler for slider value
  FutureOr<void> _onUpdateSliderValue(
      UpdateSliderValue event, Emitter<VideoTranscriptionState> emit) {
    // Clamp the value between 0 and 1.5 (matching UnityScreen slider range)
    final clampedValue = event.value.clamp(0.0, 1.5);
    // Convert slider value to animation speed (0-1.5 maps to 0.5-2.0)
    animationSpeed = (clampedValue * (2.0 - 0.5) / 1.5) + 0.5;

    emit(SliderValueUpdated(
      sliderValue: clampedValue,
      currentAnimation: state.currentAnimation,
      isTextFieldEnabled: state.isTextFieldEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isDataLoading: state.isDataLoading,
      animationSpeed: animationSpeed,
      isDisableInput: state.isDisableInput,
      isVideoSelected: state.isVideoSelected,
      isVideoUploading: state.isVideoUploading,
    ));
  }

  FutureOr<void> _onUpdateVideoSelection(
      UpdateVideoSelection event, Emitter<VideoTranscriptionState> emit) {
    emit(VideoSelected(isVideoSelected: event.isVideoSelected));
  }

  FutureOr<void> _onUploadChunkData(
      UploadChunkData event, Emitter<VideoTranscriptionState> emit) async {
    audioResponseList = [];
    try {
      isChunkUploading = true;
      emit(FileUploadLoadingState(animationSpeed: state.animationSpeed));
      final UploadSessionResponse? response = await recordingService
          .createUploadSession(event.file.name, event.sourceType);
      if (response != null) {
        final status = await uploadAudioFileInChunks(
          audioFile: File(event.file.path!),
          sessionId: response.sessionId,
          chunkSize: 1024 * 1024,
          progressNotifier: event.progressNotifier,
        );
        if (status) {
          final transcriptionStatus =
              await recordingService.startAudioChunkTranscription(
            response.sessionId,
            event.sourceType,
          );
          if (transcriptionStatus) {
            await Future.delayed(const Duration(seconds: 10), () async {
              final audioResponse = await recordingService
                  .getAudioTranscription(response.sessionId, 0);
              isChunkUploading = false;
              event.progressNotifier.value = 0.0;

              if (audioResponse != null) {
                //  audioResponse.data
                audioResponseList.addAll(audioResponse.data);
                final data = audioResponse.data.first;
                isChunkDataProcessing = true;
                for (var segment in data.root) {
                  rootWords.add(segment);
                  if (segment.contains(',')) {
                    final splitWords = segment.split(',');
                    for (int i = 0; i < splitWords.length; i++) {
                      var word = splitWords[i];
                      await Future.delayed(const Duration(milliseconds: 150),
                          () async {
                        add(const ToggleInputVisibility(showInput: false));
                        emit(const VideoTranscriptionLoaded());
                      });
                    }
                  } else {
                    add(const ToggleInputVisibility(showInput: false));
                    emit(const VideoTranscriptionLoaded());
                  }
                }
                if (audioResponse.data.last.status == "IN PROGRESS") {
                  audioChunkTimer = Timer.periodic(const Duration(seconds: 10),
                      (timer) async {
                    final audioResponse =
                        await recordingService.getAudioTranscription(
                            response.sessionId,
                            audioResponseList.last.endTime.toInt());
                    if (audioResponse != null) {
                      for (var item in audioResponse.data) {
                        audioResponseList.add(item);

                        if (audioResponse.data.last.status == "COMPLETED") {
                          timer.cancel();
                        }
                      }
                    } else {
                      timer.cancel();
                      emit(VideoTranscriptionScreenError(
                        message:
                            "Failed to process uploaded file. Please try again.",
                        animationSpeed: state.animationSpeed,
                      ));
                      emit(VideoTranscriptionInitial());
                    }
                  });
                }
              } else {
                emit(VideoTranscriptionScreenError(
                  message: "Failed to process uploaded file. Please try again.",
                  animationSpeed: state.animationSpeed,
                ));
                emit(VideoTranscriptionInitial());
              }
            });
          } else {
            isChunkUploading = false;
            emit(VideoTranscriptionScreenError(
              message: "Failed to start transcription. Please try again.",
              animationSpeed: state.animationSpeed,
            ));
          }
        } else {
          emit(VideoTranscriptionScreenError(
            message: "Failed to process uploaded file. Please try again.",
            animationSpeed: state.animationSpeed,
          ));
        }
      }
    } catch (e) {
      isChunkUploading = false;
      emit(VideoTranscriptionScreenError(
        message: "Failed to process uploaded file. Please try again.",
        animationSpeed: state.animationSpeed,
      ));
    }
  }

  Future<bool> uploadAudioFileInChunks({
    required File audioFile,
    required int sessionId,
    required int chunkSize, // e.g. 512 * 1024 for 512 KB
    required ValueNotifier<double> progressNotifier,
  }) async {
    final totalSize = await audioFile.length();
    final totalChunks = (totalSize / chunkSize).ceil();
    final raf = audioFile.openSync(mode: FileMode.read);

    int chunkNumber = 1; // Start from 1
    int uploadedBytes = 0;
    const int maxRetries = 3;

    try {
      while (chunkNumber <= totalChunks) {
        final offset = (chunkNumber - 1) * chunkSize;
        final remaining = totalSize - offset;
        final size = remaining >= chunkSize ? chunkSize : remaining;

        final chunkFile = File('${audioFile.path}_chunk_$chunkNumber.tmp');
        final chunkBytes = raf.readSync(size);
        chunkFile.writeAsBytesSync(chunkBytes);

        bool uploaded = false;
        int retryCount = 0;

        while (!uploaded && retryCount < maxRetries) {
          final response = await recordingService.uploadAudioChunk(
            audioFile: chunkFile,
            sessionId: sessionId,
            chunkNumber: chunkNumber,
            totalChunks: totalChunks,
          );

          if (response != null) {
            uploadedBytes += size;
            double progress = uploadedBytes / totalSize;
            progressNotifier.value = progress;
            uploaded = true;
          } else {
            retryCount++;
            await Future.delayed(const Duration(seconds: 2));
          }
        }

        if (!uploaded) {
          await chunkFile.delete();
          raf.closeSync();
          return false; //
        }

        await chunkFile.delete();
        chunkNumber++;
      }

      raf.closeSync();

      if (progressNotifier.value == 1.0) {
        return true; //
      } else {
        return false; // Edge case: progress not fully 1.0
      }
    } catch (e, stack) {
      debugPrintStack(stackTrace: stack);
      raf.closeSync();
      return false; //
    }
  }
}
