import 'dart:convert';

import 'package:arabic_sign_language/bloc/textTranscript/text_transcript_bloc.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';
import 'package:arabic_sign_language/utilities/overlay_utils.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart' as ui;

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';

import 'package:arabic_sign_language/presentation/core/unity_controller.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class TextTranscriptScreen extends StatefulWidget {
  const TextTranscriptScreen({super.key});

  @override
  State<TextTranscriptScreen> createState() => _TextTranscriptScreenState();
}

class _TextTranscriptScreenState extends State<TextTranscriptScreen> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocusNode = FocusNode();
  UnityWidgetController? _unityWidgetController;

  final ValueNotifier<List<String>> currentAnimationWords =
      ValueNotifier<List<String>>([]);
  final ValueNotifier<String> currentAnimationText = ValueNotifier<String>('');

  List<String> currentProcessingTexts = [];
  ValueNotifier<int> currentIndex = ValueNotifier<int>(0);
  List<String> messagesFromUnity = [];
  List<String> currentMessageList = [];

  final ValueNotifier<int> currentPageIndex = ValueNotifier<int>(0);
  int wordsPerPage = 10; // approx. 3 lines of text
  late TextTranscriptBloc _textTranscriptBloc;

  final ScrollController _statusTextScrollController = ScrollController();
  final ScrollController _statusScrollController = ScrollController();

  // Slider value for animation speed control (0.0 to 1.5)
  double sliderValue = 1.0;
  int? lastSnackIndex;

  @override
  void initState() {
    _textTranscriptBloc = context.read<TextTranscriptBloc>();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!await WakelockPlus.enabled) {
        await WakelockPlus.enable();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _textController.dispose();
    _textFocusNode.dispose();
    currentIndex.dispose();
    currentAnimationWords.dispose();
    currentAnimationText.dispose();
    messagesFromUnity.clear();

    // Clear the global reference if this controller is the current one
    if (currentUnityController == _unityWidgetController) {
      currentUnityController = null;
    }
    _textTranscriptBloc.lastTranscriptionList.clear();
    _statusTextScrollController.dispose();
    _textTranscriptBloc.add(UpdateCurrentAnimationText(animationText: ""));
    stopGlobalAnimations();
    OverlayUtils.dismissOverlay();
    super.dispose();
  }

  void onUnityCreated(controller) {
    _unityWidgetController = controller;
    setCurrentUnityController(controller); // Register this controller globally
  }

  Future<void> sendMessageToUnity(Map<String, dynamic> message) async {
    message['screen'] = "TextTranscriptScreen";

    String jsonString = json.encode(message);
    debugPrint("jsonString =>$jsonString");
    await _unityWidgetController?.postMessage(
        'SaudiCharacter', 'PlaySignAnim', jsonString);
  }

  Future<void> stopAnimations() async {
    if (_unityWidgetController != null) {
      await _unityWidgetController?.postMessage(
          'SaudiCharacter', 'StopAnimations', "");
    }
  }

  Future<void> adjustAnimationSpeed(String value) async {
    await _unityWidgetController?.postMessage(
        "SaudiCharacter", 'SetAnimationSpeed', value);
  }

  void pushToSecond(String value) {
    currentProcessingTexts.add(value);
    String combined = currentProcessingTexts.join().replaceAll(' ', '');

    final rootWordsList = context
        .read<TextTranscriptBloc>()
        .rootWords
        .map((e) => e.replaceAll(',', '').replaceAll(' ', ''))
        .toList();
    final currentFirstElement = rootWordsList[currentIndex.value];

    if (combined == currentFirstElement) {
      currentIndex.value += 1;
      currentProcessingTexts.clear();

      if (currentIndex.value % wordsPerPage == 0) {
        if ((currentPageIndex.value + 1) * wordsPerPage <
            rootWordsList.length) {
          currentPageIndex.value += 1;
        }
      }
    } else if (combined.length > currentFirstElement.length) {
      print("Warning: Combined value exceeds the current `first` element.");
    } else {
      print("Currently processing index ${currentIndex.value}");
    }
  }

  // Communication from Unity to Flutter
  Future<void> onUnityMessage(message) async {
    print("message from unity => ${message.toString()}");
    if (message.toString().contains('Current playing Animation')) {
      List<String> parts = message.split('&');
      Map<String, String> result = {};
      for (var part in parts) {
        List<String> keyValue =
            part.split('=>').map((str) => str.trim()).toList();
        if (keyValue.length == 2) {
          result[keyValue[0]] = keyValue[1];
        }
      }
      // final data = splitWords(message.toString());
      // String combinedData = result.join('  ');
      if (result["screen"] == "TextTranscriptScreen") {
        print(
            "currentAnimation unityScreen => ${result['Current playing Animation']}");
        context.read<TextTranscriptBloc>().add(UpdateCurrentAnimationText(
            animationText: result['Current playing Animation'] ?? ""));
      }
    } else if (message.toString().contains('switchtoIdle')) {
      Future.delayed(const Duration(seconds: 3), () {
        print("UpdateCurrentAnimationText => 3s");
        context
            .read<TextTranscriptBloc>()
            .add(UpdateCurrentAnimationText(animationText: ""));
        // context.read<UnityScreenBloc>().isAnimating = false;
        // currentIndex.value = 0;
        // context.read<UnityScreenBloc>().rootWords.clear();
      });
    } else if (message.toString().contains("Current Animation")) {
      if (!context
          .read<TextTranscriptBloc>()
          .arabicTextFlags[currentIndex.value]) {
        if (lastSnackIndex != currentIndex.value) {
          lastSnackIndex = currentIndex.value;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('language_not_supported'.tr()),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 1),
            ),
          );
        }
      }
      currentMessageList.add(message.toString().split("=>").last);
      print("startSpeechToTextProcessing =>${currentMessageList.length}, || "
          "messagesFromUnity => ${messagesFromUnity.length} || ${context.read<TextTranscriptBloc>().rootWords.length} ");
      pushToSecond(message.toString().split("=>").last);
      if (messagesFromUnity.length == currentMessageList.length) {
        currentIndex.value = 0;
        messagesFromUnity.clear();
        currentMessageList.clear();
        currentPageIndex.value = 0;
        context.read<TextTranscriptBloc>().rootWords.clear();
        context.read<TextTranscriptBloc>().arabicTextFlags.clear();
        context
            .read<TextTranscriptBloc>()
            .add(UpdateCurrentAnimationText(animationText: ""));
        print(
            "onUnityMessage => ${context.read<TextTranscriptBloc>().speechTexts}");
        context.read<TextTranscriptBloc>().speechTextIndex++;
        lastSnackIndex = null;
      }
    }
  }

  List<String> splitWords(String message) {
    const prefix = "Current playing Animation =>";
    if (message.startsWith(prefix)) {
      String words = message.substring(prefix.length).trim();
      return words.split(' ');
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    final replyList = context.watch<TextTranscriptBloc>().lastTranscriptionList;
    return BlocConsumer<TextTranscriptBloc, TextTranscriptState>(
      listener: (context, state) {
        print("state from text transcript screen => ${state.animationSpeed}");
        if (state is TextTranscriptUnityMessage) {
          // Send individual messages to Unity - same as UnityBloc logic

          if (_unityWidgetController != null) {
            print("TextTranscriptUnityMessage => ${state.message}");
            messagesFromUnity.add(state.message['root']);
            sendMessageToUnity(state.message);
          }
        } else if (state is TextTranscriptProcessing) {
          // Initialize animation tracking when processing starts
        } else if (state is TextTranscriptSuccess) {
          // Reset animation tracking when completed
        } else if (state is TextTranscriptError) {
          // Handle transcription error
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        } else if (state is AnimationSpeedUpdated) {
          // Update Unity animation speed when the slider value changes
          adjustAnimationSpeed(state.animationSpeed.toString());
        } else if (state is SliderValueUpdated) {
          // Update local slider value and Unity animation speed
          sliderValue = state.sliderValue;
          adjustAnimationSpeed(state.animationSpeed.toString());
        }
      },
      builder: (context, state) {
        final isLoading = state is TextTranscriptLoading || state.isDataLoading;
        final isTranslateButtonDisabled = state.isTranslateButtonDisabled;
        final isTextFieldEnabled = state.isTextFieldEnabled;
        final animationSpeed = state.animationSpeed;
        // Determine if buttons should be disabled based on animation speed
        final isIncreaseDisabled = animationSpeed >= 2.0;
        final isDecreaseDisabled = animationSpeed <= 0.5;

        return Stack(
          children: [
            Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(APP_BG), fit: BoxFit.cover),
              ),
            ),
            Container(
              padding: const EdgeInsets.fromLTRB(0, kToolbarHeight, 0, 0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  _buildHeader(context),
                  const SizedBox(height: 15),

                  // Main content
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    height: MediaQuery.of(context).size.height * 0.65,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          colors: [
                            const Color(0XFF9064FC).withOpacity(0.5),
                            const Color(0XFF1E113A).withOpacity(0.8),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        )),
                    child: Column(
                      children: [
                        const SizedBox(height: 40),

                        // Text input section
                        _buildTextInputSection(state,
                            isEnabled: isTextFieldEnabled),

                        const SizedBox(height: 30),

                        CustomGradientButton(
                            onPressed: (state.currentAnimation.isNotEmpty)
                                ? () {}
                                : _handleTranslate,
                            label: 'translate'.tr()),

                        const SizedBox(height: 30),

                        // Character avatar
                        _buildCharacterAvatar(context),

                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              right: MediaQuery.sizeOf(context).width * 0.09,
              top: MediaQuery.sizeOf(context).height * 0.465,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(23),
                  color: const Color(0XFF49446C).withOpacity(0.2),
                ),
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildControlButton(
                        Icons.refresh,
                        replyList.isNotEmpty && state.currentAnimation.isEmpty
                            ? () {
                                context
                                    .read<TextTranscriptBloc>()
                                    .add(ReplayTranscription());
                              }
                            : null,
                        replyList.isNotEmpty && state.currentAnimation.isEmpty
                            ? const Color(0XFF7B6FFF)
                            : Colors.grey.withOpacity(0.8)),
                    _verticalSlider(),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: MediaQuery.sizeOf(context).height * 0.12,
              child: // Status text
                  _buildStatusText(state, MediaQuery.sizeOf(context)),
            ),
            // Add vertical slider for animation speed control
          ],
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          // Back button
          GestureDetector(
              onTap: () {
                stopAnimations();
                context
                    .read<TextTranscriptBloc>()
                    .add(UpdateCurrentAnimationText(animationText: ""));
                bottomBarIndex.value = 0;
              },
              child: Image.asset(BACK_BUTTON, height: 25, width: 25)),

          const SizedBox(width: 15),

          // Title
          Text(
            'translate_text'.tr(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: FONT_FAMILY,
            ),
          ),

          const Spacer(),

          // Info button
          GestureDetector(
              onTap: () {
                // Show info dialog or tooltip
                OverlayUtils.showInfoOverlay(
                    context, "text_translation_info".tr());
              },
              child: Image.asset(INFO, height: 25, width: 25)),

          const SizedBox(width: 10),

          // Language toggle button
          // BlocBuilder<LanguageBloc, LanguageState>(
          //   builder: (context, state) {
          //     return GestureDetector(
          //       onTap: () {
          //         // Toggle language
          //         context.read<LanguageBloc>().add(ToggleLanguage());

          //         // Set locale based on current language
          //         final newLocale = state.locale.languageCode == 'en'
          //             ? const Locale('ar')
          //             : const Locale('en');

          //         context.setLocale(newLocale);

          //         // Force rebuild by triggering a state change
          //         setState(() {});

          //         // Debug print to verify locale change
          //         print("Language changed to: ${newLocale.languageCode}");
          //       },
          //       child: Container(
          //         padding: const EdgeInsets.all(8),
          //         decoration: BoxDecoration(
          //           color: Colors.white.withOpacity(0.1),
          //           borderRadius: BorderRadius.circular(20),
          //         ),
          //         child: Text(
          //           state.locale.languageCode == 'en' ? 'AR' : 'EN',
          //           style: const TextStyle(
          //             color: Colors.white,
          //             fontSize: 14,
          //             fontWeight: FontWeight.w500,
          //           ),
          //         ),
          //       ),
          //     );
          //   },
          // ),
        ],
      ),
    );
  }

  Widget _buildTextInputSection(TextTranscriptState state,
      {bool isEnabled = true}) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.15,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: const Color(0XFF221B67),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // Text input field
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextFormField(
              controller: _textController,
              focusNode: _textFocusNode,
              enabled: state.currentAnimation.isEmpty,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: FONT_FAMILY,
              ),
              decoration: InputDecoration(
                hintText: 'enter_your_text'.tr(), // Use translation key
                hintStyle: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 16,
                  fontFamily: FONT_FAMILY,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),

          // Camera icon positioned at bottom right
          Positioned(
            bottom: 12,
            right: 12,
            child: GestureDetector(
              onTap: () {
                // Handle camera functionality
                _handleCameraInput();
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterAvatar(BuildContext context) {
    return Expanded(
      child: Container(
        height: MediaQuery.sizeOf(context).height * 0.1,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white.withOpacity(0.1),
              Colors.white.withOpacity(0.05),
            ],
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // Character image
              UnityWidget(
                onUnityCreated: onUnityCreated,
                onUnityMessage: onUnityMessage,
              ),

              // Control buttons
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    VoidCallback? onTap,
    Color backgroundColor,
  ) {
    return Material(
      // Circle with your background color.
      color: backgroundColor,
      shape: const CircleBorder(),
      clipBehavior: Clip.antiAlias, // keeps splash inside the circle
      child: InkWell(
        onTap: onTap,
        customBorder: const CircleBorder(), // matches the shape
        splashColor: Colors.white24, // optional – tweak to taste
        highlightColor: Colors.white10, // optional – tweak to taste
        child: SizedBox(
          width: 40,
          height: 40,
          child: Icon(
            icon,
            color: Colors.white,
            size: 18,
          ),
        ),
      ),
    );
  }

  void _handleTranslate() {
    if (_textController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('please_enter_text'.tr()), // Use translation key
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    // Trigger the BLoC event to process transcription
    context.read<TextTranscriptBloc>().add(
          ProcessTextTranscription(
            inputText: _textController.text,
            inputTextController: _textController,
            inputFocusNode: _textFocusNode,
            sourceType: "text_translate",
          ),
        );
  }

  void _handleCameraInput() {
    // Handle camera functionality for text recognition
    // This could open camera to capture text or image
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('camera_not_implemented'.tr()), // Use translation key
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Animation tracking methods - similar to UnityScreen
  void _initializeAnimationTracking(dynamic state) {
    // Extract all words from the original text for animation tracking
    String fullText = '';
    if (state is TextTranscriptProcessing) {
      fullText = state.originalText;
    } else if (state is TextTranscriptUnityMessage) {
      fullText = state.originalText;
    }

    List<String> words =
        fullText.split(' ').where((word) => word.isNotEmpty).toList();

    currentAnimationText.value = fullText;
    currentAnimationWords.value = words;
    currentIndex.value = -1; // Start with no word highlighted
  }

  void _updateCurrentAnimation(TextTranscriptUnityMessage state) {
    // Find the current word being animated and update the index
    String currentRoot = state.message['root']?.toString() ?? '';
    List<String> words = currentAnimationWords.value;

    // Find the index of the current word being animated
    for (int i = 0; i < words.length; i++) {
      if (words[i].contains(currentRoot) || currentRoot.contains(words[i])) {
        currentIndex.value = i;
        break;
      }
    }
  }

  Widget _currentAnimation(Size size) {
    return BlocBuilder<TextTranscriptBloc, TextTranscriptState>(
      builder: (context, state) {
        List<String> words = state.currentAnimation
            .replaceAll('[', '')
            .replaceAll(']', '')
            .split(', ');
        return SizedBox(
          width: size.width * 0.7,
          child: ValueListenableBuilder(
              valueListenable: currentIndex,
              builder: (context, currentActiveIndex, _) {
                return Text.rich(
                  TextSpan(
                    children: List<TextSpan>.generate(words.length, (index) {
                      return TextSpan(
                        text:
                            '${words[index]}${index < words.length - 1 ? ' ' : ''}', // Add space between words
                        style: TextStyle(
                          color: index == currentActiveIndex
                              ? Colors.red
                              : Colors.black, // Highlighted color
                          fontWeight: index == currentActiveIndex
                              ? FontWeight.bold
                              : FontWeight.normal, // Bold for highlighted
                          fontSize: 18,
                        ),
                      );
                    }),
                  ),
                  textAlign: TextAlign.center,
                );
              }),
        );
      },
    );
  }

  // Current animation widget - similar to UnityScreen
  Widget _buildCurrentAnimation() {
    return ValueListenableBuilder<String>(
      valueListenable: currentAnimationText,
      builder: (context, animationText, child) {
        if (animationText.isEmpty) {
          return const SizedBox.shrink();
        }

        return ValueListenableBuilder<List<String>>(
          valueListenable: currentAnimationWords,
          builder: (context, words, child) {
            if (words.isEmpty) {
              return const SizedBox.shrink();
            }

            return ValueListenableBuilder<int>(
              valueListenable: currentIndex,
              builder: (context, index, child) {
                return Container(
                  width: MediaQuery.of(context).size.width,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: words.asMap().entries.map((entry) {
                      int wordIndex = entry.key;
                      String word = entry.value;
                      bool isCurrentWord = wordIndex == index;

                      return Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4, vertical: 2),
                        child: Text(
                          word,
                          style: TextStyle(
                            color: isCurrentWord ? Colors.red : Colors.white,
                            fontSize: 16,
                            fontWeight: isCurrentWord
                                ? FontWeight.bold
                                : FontWeight.normal,
                            fontFamily: FONT_FAMILY,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildStatusText(TextTranscriptState state, Size size) {
    List<String> allWords = state.currentAnimation
        .replaceAll('[', '')
        .replaceAll(']', '')
        .split(', ');

    int totalSegments = (allWords.length / wordsPerPage).ceil();

    return Container(
      width: size.width,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: ValueListenableBuilder<int>(
        valueListenable: currentPageIndex,
        builder: (context, currentPage, _) {
          // Auto-scroll to current segment
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_statusScrollController.hasClients) {
              _statusScrollController.animateTo(
                currentPage * 100.0, // Adjust height as per segment height
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              );
            }
          });

          return SizedBox(
            height: 120, // Set height so scroll is visible
            child: ListView.builder(
              controller: _statusScrollController,
              itemCount: totalSegments,
              itemBuilder: (context, segmentIndex) {
                final start = segmentIndex * wordsPerPage;
                final end = (start + wordsPerPage).clamp(0, allWords.length);
                final visibleWords = allWords.sublist(start, end);

                return Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: ValueListenableBuilder<int>(
                    valueListenable: currentIndex,
                    builder: (context, currentActiveIndex, _) {
                      return Text.rich(
                        TextSpan(
                          children: List.generate(visibleWords.length, (index) {
                            final globalIndex = start + index;
                            return TextSpan(
                              text:
                                  '${visibleWords[index]}${index < visibleWords.length - 1 ? ' ' : ''}',
                              style: TextStyle(
                                backgroundColor:
                                    globalIndex == currentActiveIndex
                                        ? const Color(0XFF755BFF)
                                        : null,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            );
                          }),
                        ),
                        textDirection: ui.TextDirection.rtl,
                      );
                    },
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _verticalSlider() {
    final isRtl = Directionality.of(context) == ui.TextDirection.rtl;

    return Container(
      width: 50,
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      height: MediaQuery.of(context).size.height * 0.15,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: Container(
                width: 50,
                margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFE8E8E8),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: SliderTheme(
                  data: SliderThemeData(
                    overlayShape: SliderComponentShape.noOverlay,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 15,
                      disabledThumbRadius: 20,
                    ),
                  ),
                  child: BlocBuilder<TextTranscriptBloc, TextTranscriptState>(
                    builder: (context, state) {
                      if (state is SliderValueUpdated) {
                        sliderValue = state.sliderValue;
                      }

                      final displayValue =
                          isRtl ? (1.5 - sliderValue) : sliderValue;

                      return Slider(
                        activeColor: const Color(0xFFE8E8E8),
                        inactiveColor: const Color(0xFFE8E8E8),
                        thumbColor: const Color(0xFF054DA4),
                        value: displayValue,
                        min: 0,
                        max: 1.5,
                        divisions: 3,
                        onChanged: (val) {
                          final newValue = isRtl ? (1.5 - val) : val;
                          context
                              .read<TextTranscriptBloc>()
                              .add(UpdateSliderValue(value: newValue));
                        },
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
